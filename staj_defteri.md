# 11 GÜNLÜK STAJ DEFTERİ
**Stajyer:** [Adı<PERSON><PERSON><PERSON>]  
**<PERSON>aj Yeri:** Türkiye Nükleer Enerji A.Ş. (TÜNAŞ)  
**Staj Süresi:** 11 Gün  
**Proje:** Talep Yönetim Sistemi

---

## 1. GÜN - React Temelleri

### Çalışmanın Özeti
Bugün React öğrenmeye başladım. Temel kavramları öğrendim ve basit bir React uygulaması oluşturdum.

### Öğrenilenler
- React nedir ve nasıl çalışır
- JSX syntax yapısı
- Component kavramı ve functional componentler
- Props kullanımı
- State yönetimi (useState hook)
- Event handling

### Karşılaşılan Zorluklar
- JSX syntax'ına alışmak biraz zaman aldı
- State ve props arasındaki farkı kavramak

### Gelecek Planları
Yarın React hooks konusunu derinleştireceğim ve API çağrıları yapmayı öğreneceğim.

---

## 2. GÜN - React Hooks ve API Entegrasyonu

### Çalışmanın Özeti
React hooks konusunu derinleştirdim ve API çağrıları yapmayı öğrendim.

### Öğrenilenler
- useEffect hook kullanımı
- API çağrıları için axios kütüphanesi
- Async/await yapısı
- Loading ve error state yönetimi
- Component lifecycle

### Karşılaşılan Zorluklar
- useEffect dependency array kavramını anlamak
- Async işlemlerde state güncellemelerini doğru yapmak

### Gelecek Planları
Yarın form yönetimi ve routing konularını öğreneceğim.

---

## 3. GÜN - Form Yönetimi ve Routing

### Çalışmanın Özeti
React'te form yönetimi ve routing konularını öğrendim. Basit bir login formu oluşturdum.

### Öğrenilenler
- Controlled components
- Form validation
- React Router kullanımı
- Context API ile state yönetimi
- CSS modules

### Karşılaşılan Zorluklar
- Form validation mantığını kurmak
- Context API'yi doğru şekilde kullanmak

### Gelecek Planları
Yarından itibaren şirketin Talep Yönetim Sistemi projesinde çalışmaya başlayacağım.

---

## 4. GÜN - Proje Analizi ve Login Sistemi

### Çalışmanın Özeti
Bugün Talep Yönetim Sistemi projesinin genel yapısını inceledim ve login sistemini geliştirdim.

### Yapılan İşler
- Projenin backend (.NET Core Web API) ve frontend (React) yapısını analiz ettim
- Login.js componentini inceledim ve geliştirdim
- AuthContext.js ile authentication state yönetimini kurdum
- Login formunda personel numarası ve şifre doğrulama işlemlerini tamamladım

### Karşılaşılan Zorluklar
- Backend API ile frontend arasındaki CORS ayarlarını anlamak
- Authentication context yapısını kurmak

### Gelecek Planları
Yarın register sistemi ve user dashboard geliştirmesine başlayacağım.

---

## 5. GÜN - Kayıt Sistemi ve User Dashboard

### Çalışmanın Özeti
Bugün kullanıcı kayıt sistemini tamamladım ve user dashboard'un temel yapısını oluşturdum.

### Yapılan İşler
- Register.js componentini geliştirdim
- Kullanıcı kayıt formunu tamamladım (personel numarası, şifre, ad, soyad, departman)
- UserDashboard.js'in temel navigasyon yapısını kurdum
- Navbar ve Footer componentlerini entegre ettim

### Karşılaşılan Zorluklar
- Form validation işlemlerini doğru yapmak
- Dashboard navigasyon yapısını responsive hale getirmek

### Gelecek Planları
Yarın talep oluşturma sistemi üzerinde çalışacağım.

---

## 6. GÜN - Talep Oluşturma Sistemi

### Çalışmanın Özeti
Bugün kullanıcıların talep oluşturabileceği sistemi geliştirdim.

### Yapılan İşler
- CreateRequest.js componentini tamamladım
- Modal yapısında talep oluşturma formu geliştirdim
- Talep başlığı ve içeriği için form validasyonu ekledim
- Backend API ile talep oluşturma entegrasyonunu yaptım
- Kullanıcının kendi taleplerini görüntüleyebileceği listeyi oluşturdum

### Karşılaşılan Zorluklar
- Modal component yapısını doğru kurmak
- API response'larını doğru handle etmek

### Gelecek Planları
Yarın admin dashboard ve talep yönetimi sistemini geliştireceğim.

---

## 7. GÜN - Admin Dashboard ve Talep Yönetimi

### Çalışmanın Özeti
Bugün admin panelini geliştirdim ve talep yönetimi sistemini tamamladım.

### Yapılan İşler
- AdminDashboard.js componentini oluşturdum
- Admin için talep listesi ve istatistik kartları ekledim
- Bekleyen ve cevaplanmış talep sayılarını gösteren dashboard oluşturdum
- Admin'in taleplere cevap verebileceği sistemi geliştirdim
- Talep durumu güncelleme işlevini ekledim

### Karşılaşılan Zorluklar
- Admin ve user dashboard'ları arasındaki farkları doğru yönetmek
- Talep durumu güncellemelerini real-time yapmak

### Gelecek Planları
Yarın favori talepler ve todo list sistemlerini geliştireceğim.

---

## 8. GÜN - Favori Talepler ve Todo List Sistemi

### Çalışmanın Özeti
Bugün kullanıcıların favori taleplerini yönetebileceği sistemi ve todo list özelliğini geliştirdim.

### Yapılan İşler
- FavoriteRequests.js componentini oluşturdum
- Kullanıcıların talepleri favorilere ekleyip çıkarabilme özelliği ekledim
- TodoList.js componentini geliştirdim
- Todo ekleme, silme ve tamamlama işlevlerini tamamladım
- Todo öncelik seviyeleri (Low, Normal, High, Urgent) ekledim

### Karşılaşılan Zorluklar
- Favori işlemlerinin backend ile senkronizasyonu
- Todo list'te drag-drop özelliği eklemek

### Gelecek Planları
Yarın anket sistemi ve duyuru yönetimini geliştireceğim.

---

## 9. GÜN - Anket Sistemi ve Duyuru Yönetimi

### Çalışmanın Özeti
Bugün anket sistemi ve duyuru yönetimi özelliklerini geliştirdim.

### Yapılan İşler
- Surveys.js componentini oluşturdum
- Kullanıcıların anketleri görüntüleyip cevaplayabileceği sistemi geliştirdim
- SurveyCreate.js ile admin'in anket oluşturabileceği formu tamamladım
- Announcements.js componentini geliştirdim
- AnnouncementManagement.js ile duyuru oluşturma ve yönetim sistemini kurdum

### Karşılaşılan Zorluklar
- Anket sorularının dinamik yapısını kurmak
- Duyuru tiplerini (Announcement, News) doğru kategorize etmek

### Gelecek Planları
Yarın etkinlik takvimi sistemini geliştireceğim.

---

## 10. GÜN - Etkinlik Takvimi Sistemi

### Çalışmanın Özeti
Bugün etkinlik takvimi sistemini geliştirdim ve admin'in etkinlik yönetimi yapabileceği paneli oluşturdum.

### Yapılan İşler
- EventCalendar.js componentini oluşturdum
- Takvim görünümünde etkinlikleri listeleme özelliği ekledim
- EventManagement.js ile admin'in etkinlik oluşturabileceği sistemi geliştirdim
- Etkinlik türleri, tarih aralıkları ve lokasyon bilgilerini yönetme özelliği ekledim
- Tam gün etkinlik ve renkli kategorilendirme sistemi kurdum

### Karşılaşılan Zorluklar
- Takvim görünümünü responsive hale getirmek
- Tarih ve saat işlemlerini doğru handle etmek

### Gelecek Planları
Yarın projeyi son kez gözden geçirip test işlemlerini tamamlayacağım.

---

## 11. GÜN - Proje Tamamlama ve Test İşlemleri

### Çalışmanın Özeti
Bugün projeyi son kez gözden geçirdim, test işlemlerini tamamladım ve genel optimizasyonları yaptım.

### Yapılan İşler
- Tüm componentlerin çalışmasını test ettim
- CSS stillerini optimize ettim ve responsive tasarım iyileştirmeleri yaptım
- API error handling'lerini geliştirdim
- Loading state'lerini tüm componentlerde standardize ettim
- AboutTunas.js componentini tamamladım
- Genel kod temizliği ve optimizasyon işlemlerini yaptım

### Karşılaşılan Zorluklar
- Tüm componentlerin birbiriyle uyumlu çalışmasını sağlamak
- Performance optimizasyonları yapmak

### Gelecek Planları
Proje başarıyla tamamlandı. Gelecekte ek özellikler eklenebilir (bildirim sistemi, dosya yükleme, gelişmiş raporlama vb.).

---

## GENEL DEĞERLENDİRME

### Proje Özeti
11 günlük staj süresince Türkiye Nükleer Enerji A.Ş. için kapsamlı bir Talep Yönetim Sistemi geliştirdim. Proje .NET Core Web API backend ve React frontend teknolojileri kullanılarak oluşturuldu.

### Geliştirilen Özellikler
- Kullanıcı authentication sistemi (login/register)
- Talep oluşturma ve yönetimi
- Admin paneli ve talep cevaplama sistemi
- Favori talepler yönetimi
- Todo list sistemi
- Anket sistemi
- Duyuru yönetimi
- Etkinlik takvimi

### Kazanılan Deneyimler
- React framework'ünde derinlemesine bilgi edindim
- Modern web geliştirme teknikleri öğrendim
- API entegrasyonu konusunda deneyim kazandım
- Proje yönetimi ve planlama becerilerimi geliştirdim

### Sonuç
Staj süresi boyunca hem teknik becerilerimi geliştirdim hem de kurumsal bir projede çalışma deneyimi kazandım. Proje başarıyla tamamlandı ve kullanıma hazır hale getirildi.
